package com.investment.service

import com.investment.api.model.CreatePositionRequest
import com.investment.api.model.UpdatePositionRequest
import com.investment.api.model.PositionsUpdateRequest
import com.investment.database.DatabaseManager
import com.investment.model.Position
import com.investment.service.OHLCVService
import spock.lang.Specification

import java.math.BigDecimal
import java.sql.Connection
import java.sql.PreparedStatement
import java.sql.ResultSet
import java.sql.SQLException
import java.sql.Statement
import java.sql.Timestamp
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Test specification for PositionsService.
 */
class PositionsServiceSpec extends Specification {

    PositionsService positionsService
    DatabaseManager databaseManager
    OHLCVService ohlcvService

    def setup() {
        databaseManager = Mock(DatabaseManager)
        ohlcvService = Mock(OHLCVService)
        positionsService = new PositionsService(databaseManager, ohlcvService)
    }

    // Helper method to create sample positions for testing
    private Position createSamplePosition(Long id, String symbol) {
        def position = new Position(symbol, new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.00"))
        position.setId(id)
        position.setStatus(Position.Status.OPEN)
        position.setTradeValue(new BigDecimal("15000.00"))
        return position
    }

    def "should create position successfully"() {
        given: "a valid create position request"
        def request = new CreatePositionRequest("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        request.setInitPortfolioNetValue(new BigDecimal("100000"))
        request.setRiskUnit(new BigDecimal("1000"))
        request.setStopPercent(new BigDecimal("0.02"))

        and: "symbol exists in instruments table"
        databaseManager.symbolExists("AAPL") >> true

        and: "database creates position successfully"
        databaseManager.createPosition(
                "AAPL",
                new BigDecimal("100"),
                "BUY",
                "OPEN",
                new BigDecimal("150.25"),
                new BigDecimal("15025.00"),
                new BigDecimal("100000"),
                new BigDecimal("1000"),
                new BigDecimal("0.02"),
                null
        ) >> 1L

        when: "creating the position"
        def position = positionsService.createPosition(request)

        then: "position should be created with correct data"
        position.id == 1L
        position.symbol == "AAPL"
        position.position == new BigDecimal("100")
        position.side == Position.Side.BUY
        position.tradePrice == new BigDecimal("150.25")
        position.tradeValue == new BigDecimal("15025.00")
        position.status == Position.Status.OPEN
        position.initPortfolioNetValue == new BigDecimal("100000")
        position.riskUnit == new BigDecimal("1000")
        position.stopPercent == new BigDecimal("0.02")
    }

    def "should throw exception when symbol does not exist"() {
        given: "a create position request with non-existent symbol"
        def request = new CreatePositionRequest("INVALID", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))

        and: "symbol does not exist in instruments table"
        databaseManager.symbolExists("INVALID") >> false

        when: "creating the position"
        positionsService.createPosition(request)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Symbol not found in instruments: INVALID")
    }

    def "should get position by ID successfully"() {
        given: "a position ID"
        def positionId = 1L

        and: "database returns position data"
        def positionData = [
                id: 1L,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.25"),
                trade_value: new BigDecimal("15025.00"),
                init_portfolio_net_value: new BigDecimal("100000"),
                last_price: new BigDecimal("155.00"),
                last_value: new BigDecimal("15500.00"),
                risk_unit: new BigDecimal("1000"),
                stop_percent: new BigDecimal("0.02"),
                highest_after_trade: new BigDecimal("155.00"),
                stop_value_from_highest: new BigDecimal("151.90"),
                last_bbmb: new BigDecimal("152.00"),
                bbmb_adj_percent: new BigDecimal("0.01"),
                stop_value_from_bbmb: new BigDecimal("150.48"),
                expand_or_contract: "EXPANDING",
                effective_stop_value: new BigDecimal("151.90"),
                pnl_value: new BigDecimal("475.00"),
                pnl_percent: new BigDecimal("0.0316"),
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "getting position by ID"
        def optionalPosition = positionsService.getPositionById(positionId)

        then: "should return position with correct data"
        optionalPosition.isPresent()
        def position = optionalPosition.get()
        position.id == 1L
        position.symbol == "AAPL"
        position.position == new BigDecimal("100")
        position.side == Position.Side.BUY
        position.status == Position.Status.OPEN
        position.expandOrContract == Position.ExpandOrContract.EXPANDING
    }

    def "should return empty optional when position not found"() {
        given: "a position ID that doesn't exist"
        def positionId = 999L

        and: "database returns no results"
        databaseManager.getPositionById(positionId) >> null

        when: "getting position by ID"
        def optionalPosition = positionsService.getPositionById(positionId)

        then: "should return empty optional"
        !optionalPosition.isPresent()
    }

    def "should get positions with filters"() {
        given: "filter parameters"
        def symbol = "AAPL"
        def status = Position.Status.OPEN
        def side = Position.Side.BUY

        and: "database returns filtered results"
        def positionsData = [
                [
                        id: 1L,
                        symbol: "AAPL",
                        position: new BigDecimal("100"),
                        side: "BUY",
                        status: "OPEN",
                        trade_price: new BigDecimal("150.25"),
                        trade_value: new BigDecimal("15025.00"),
                        init_portfolio_net_value: null,
                        last_price: null,
                        last_value: null,
                        risk_unit: null,
                        stop_percent: null,
                        highest_after_trade: null,
                        stop_value_from_highest: null,
                        last_bbmb: null,
                        bbmb_adj_percent: null,
                        stop_value_from_bbmb: null,
                        expand_or_contract: null,
                        effective_stop_value: null,
                        pnl_value: null,
                        pnl_percent: null,
                        created_date: Timestamp.valueOf(LocalDateTime.now()),
                        updated_date: Timestamp.valueOf(LocalDateTime.now())
                ],
                [
                        id: 2L,
                        symbol: "AAPL",
                        position: new BigDecimal("200"),
                        side: "BUY",
                        status: "OPEN",
                        trade_price: new BigDecimal("148.50"),
                        trade_value: new BigDecimal("29700.00"),
                        init_portfolio_net_value: null,
                        last_price: null,
                        last_value: null,
                        risk_unit: null,
                        stop_percent: null,
                        highest_after_trade: null,
                        stop_value_from_highest: null,
                        last_bbmb: null,
                        bbmb_adj_percent: null,
                        stop_value_from_bbmb: null,
                        expand_or_contract: null,
                        effective_stop_value: null,
                        pnl_value: null,
                        pnl_percent: null,
                        created_date: Timestamp.valueOf(LocalDateTime.now()),
                        updated_date: Timestamp.valueOf(LocalDateTime.now())
                ]
        ]

        databaseManager.getPositions("AAPL", "OPEN", "BUY") >> positionsData

        when: "getting positions with filters"
        def positions = positionsService.getPositions(symbol, status, side)

        then: "should return filtered positions"
        positions.size() == 2
        positions[0].symbol == "AAPL"
        positions[0].side == Position.Side.BUY
        positions[0].status == Position.Status.OPEN
        positions[1].symbol == "AAPL"
        positions[1].side == Position.Side.BUY
        positions[1].status == Position.Status.OPEN
    }

    def "should update position successfully"() {
        given: "an existing position"
        def positionId = 1L
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setLastPrice(new BigDecimal("155.00"))
        updateRequest.setRiskUnit(new BigDecimal("1200"))
        updateRequest.setStatus(Position.Status.CLOSED)

        and: "position exists in database"
        def positionData = [
                id: positionId,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.25"),
                trade_value: new BigDecimal("15025.00"),
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "updating the position"
        def updatedPosition = positionsService.updatePosition(positionId, updateRequest)

        then: "position should be updated"
        updatedPosition.id == positionId
        updatedPosition.lastPrice == new BigDecimal("155.00")
        updatedPosition.riskUnit == new BigDecimal("1200")
        updatedPosition.status == Position.Status.CLOSED

        and: "database should be updated"
        1 * databaseManager.updatePosition(positionId, _, _, _, _, _, _, _, _, _, _, _, _, _, "CLOSED", _)
    }

    def "should throw exception when updating non-existent position"() {
        given: "a non-existent position ID"
        def positionId = 999L
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setLastPrice(new BigDecimal("155.00"))

        and: "position does not exist"
        databaseManager.getPositionById(positionId) >> null

        when: "updating the position"
        positionsService.updatePosition(positionId, updateRequest)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Position not found: 999")
    }

    def "should close position successfully"() {
        given: "an existing open position"
        def positionId = 1L

        and: "position exists in database"
        def positionData = [
                id: positionId,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.25"),
                trade_value: new BigDecimal("15025.00"),
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "closing the position"
        def closedPosition = positionsService.closePosition(positionId)

        then: "position should be closed"
        closedPosition.status == Position.Status.CLOSED
        closedPosition.updatedDate != null

        and: "database should be updated"
        1 * databaseManager.updatePosition(positionId, _, _, _, _, _, _, _, _, _, _, _, _, _, "CLOSED", _)
    }

    def "should delete position successfully"() {
        given: "a position ID"
        def positionId = 1L

        and: "database deletes position successfully"
        databaseManager.deletePosition(positionId) >> true

        when: "deleting the position"
        def deleted = positionsService.deletePosition(positionId)

        then: "should return true"
        deleted
    }

    def "should return false when deleting non-existent position"() {
        given: "a non-existent position ID"
        def positionId = 999L

        and: "database returns false for deletion"
        databaseManager.deletePosition(positionId) >> false

        when: "deleting the position"
        def deleted = positionsService.deletePosition(positionId)

        then: "should return false"
        !deleted
    }

    def "should get open positions"() {
        given: "database returns open positions"
        def openPositionsData = [
                [
                        id: 1L,
                        symbol: "AAPL",
                        position: new BigDecimal("100"),
                        side: "BUY",
                        status: "OPEN",
                        trade_price: new BigDecimal("150.25"),
                        trade_value: new BigDecimal("15025.00"),
                        init_portfolio_net_value: null,
                        last_price: null,
                        last_value: null,
                        risk_unit: null,
                        stop_percent: null,
                        highest_after_trade: null,
                        stop_value_from_highest: null,
                        last_bbmb: null,
                        bbmb_adj_percent: null,
                        stop_value_from_bbmb: null,
                        expand_or_contract: null,
                        effective_stop_value: null,
                        pnl_value: null,
                        pnl_percent: null,
                        created_date: Timestamp.valueOf(LocalDateTime.now()),
                        updated_date: Timestamp.valueOf(LocalDateTime.now())
                ]
        ]

        databaseManager.getPositions(null, "OPEN", null) >> openPositionsData

        when: "getting open positions"
        def positions = positionsService.getOpenPositions()

        then: "should return open positions"
        positions.size() == 1
        positions[0].status == Position.Status.OPEN
    }

    def "should handle SQL exceptions gracefully"() {
        given: "database throws SQLException"
        databaseManager.getPositionById(1L) >> { throw new SQLException("Database error") }

        when: "performing database operation"
        positionsService.getPositionById(1L)

        then: "should propagate SQLException"
        thrown(SQLException)
    }

    def "should update position with close price and calculate P&L"() {
        given: "an existing BUY position"
        def positionId = 1L
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setClosePrice(new BigDecimal("155.00"))

        and: "position exists in database"
        def positionData = [
                id: positionId,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.00"),
                trade_value: new BigDecimal("15000.00"),
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                close_price: null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "updating position with close price"
        def updatedPosition = positionsService.updatePosition(positionId, updateRequest)

        then: "position should have close price and calculated P&L"
        updatedPosition.closePrice == new BigDecimal("155.00")
        updatedPosition.pnlValue == new BigDecimal("500.00") // (155 - 150) * 100
        updatedPosition.pnlPercent.compareTo(new BigDecimal("0.033333")) == 0 // 500 / 15000

        and: "database should be updated with P&L values"
        1 * databaseManager.updatePosition(positionId, _, _, _, _, _, _, _, _, _, _, _,
                                          new BigDecimal("500.00"), _, _, new BigDecimal("155.00"))
    }

    def "should update close price directly and calculate P&L"() {
        given: "an existing SELL position"
        def positionId = 1L
        def closePrice = new BigDecimal("145.00")

        and: "position exists in database"
        def positionData = [
                id: positionId,
                symbol: "AAPL",
                position: new BigDecimal("-100"), // SELL position
                side: "SELL",
                status: "OPEN",
                trade_price: new BigDecimal("150.00"),
                trade_value: new BigDecimal("-15000.00"), // Negative for SELL
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                close_price: null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "updating close price directly"
        def updatedPosition = positionsService.updateClosePrice(positionId, closePrice)

        then: "position should have close price and calculated P&L for SELL"
        updatedPosition.closePrice == new BigDecimal("145.00")
        updatedPosition.pnlValue == new BigDecimal("500.00") // 15000 - 14500 for SELL
        updatedPosition.pnlPercent.compareTo(new BigDecimal("0.033333")) == 0 // 500 / 15000

        and: "database should be updated with P&L values"
        1 * databaseManager.updatePosition(positionId, _, _, _, _, _, _, _, _, _, _, _,
                                          new BigDecimal("500.00"), _, _, new BigDecimal("145.00"))
    }

    def "should throw exception when updating close price with invalid value"() {
        given: "invalid close price"
        def positionId = 1L
        def invalidClosePrice = new BigDecimal("-10.00")

        when: "updating close price with negative value"
        positionsService.updateClosePrice(positionId, invalidClosePrice)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Close price must be positive")
    }

    def "should throw exception when updating close price with null value"() {
        given: "null close price"
        def positionId = 1L

        when: "updating close price with null"
        positionsService.updateClosePrice(positionId, null)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Close price must be positive")
    }

    def "should throw exception when updating close price for non-existent position"() {
        given: "a non-existent position ID"
        def positionId = 999L
        def closePrice = new BigDecimal("155.00")

        and: "position does not exist"
        databaseManager.getPositionById(positionId) >> null

        when: "updating close price"
        positionsService.updateClosePrice(positionId, closePrice)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Position not found: 999")
    }

    def "should close position with close price and calculate P&L"() {
        given: "an existing open position"
        def positionId = 1L
        def closePrice = new BigDecimal("160.00")

        and: "position exists in database"
        def positionData = [
                id: positionId,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.00"),
                trade_value: new BigDecimal("15000.00"),
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                close_price: null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "closing position with close price"
        def closedPosition = positionsService.closePositionWithPrice(positionId, closePrice)

        then: "position should be closed with P&L calculated"
        closedPosition.status == Position.Status.CLOSED
        closedPosition.closePrice == new BigDecimal("160.00")
        closedPosition.pnlValue == new BigDecimal("1000.00") // (160 - 150) * 100
        closedPosition.pnlPercent.compareTo(new BigDecimal("0.066667")) == 0 // 1000 / 15000

        and: "database should be updated"
        1 * databaseManager.updatePosition(positionId, _, _, _, _, _, _, _, _, _, _, _,
                                          new BigDecimal("1000.00"), _, "CLOSED", new BigDecimal("160.00"))
    }

    def "should update OHLCV data for positions successfully"() {
        given: "positions exist in database"
        def positions = [
                createSamplePosition(1L, "AAPL"),
                createSamplePosition(2L, "MSFT"),
                createSamplePosition(3L, "AAPL") // Duplicate symbol to test deduplication
        ]
        databaseManager.getPositions(null, null, null) >> positions.collect { pos ->
            [
                    id: pos.id,
                    symbol: pos.symbol,
                    position: pos.position,
                    side: pos.side.name(),
                    status: pos.status.name(),
                    trade_price: pos.tradePrice,
                    trade_value: pos.tradeValue,
                    init_portfolio_net_value: pos.initPortfolioNetValue,
                    last_price: pos.lastPrice,
                    last_value: pos.lastValue,
                    risk_unit: pos.riskUnit,
                    stop_percent: pos.stopPercent,
                    highest_after_trade: pos.highestAfterTrade,
                    stop_value_from_highest: pos.stopValueFromHighest,
                    last_bbmb: pos.lastBbmb,
                    bbmb_adj_percent: pos.bbmbAdjPercent,
                    stop_value_from_bbmb: pos.stopValueFromBbmb,
                    expand_or_contract: pos.expandOrContract?.name(),
                    effective_stop_value: pos.effectiveStopValue,
                    pnl_value: pos.pnlValue,
                    pnl_percent: pos.pnlPercent,
                    close_price: pos.closePrice,
                    created_date: Timestamp.valueOf(LocalDateTime.now()),
                    updated_date: Timestamp.valueOf(LocalDateTime.now())
            ]
        }

        and: "OHLCV service updates successfully"
        ohlcvService.updateOHLCVData("AAPL", null, null) >> 5
        ohlcvService.updateOHLCVData("MSFT", null, null) >> 3

        and: "latest market prices are available"
        databaseManager.getLatestOHLCVData("AAPL", 1) >> [[close: 155.50d]]
        databaseManager.getLatestOHLCVData("MSFT", 1) >> [[close: 285.75d]]

        when: "updating OHLCV data for positions"
        def request = new PositionsUpdateRequest(false, true)
        def result = positionsService.updateOHLCVDataForPositions(request)

        then: "should process unique symbols and update P&L"
        result.totalSymbols == 2 // AAPL and MSFT (deduplicated)
        result.ohlcvSuccessCount == 2
        result.ohlcvErrorCount == 0
        result.totalRecordsUpdated == 8 // 5 + 3
        result.pnlUpdatedCount == 3 // All 3 positions
        result.totalPositions == 3
        result.summary.contains("Updated OHLCV data for 2/2 symbols")

        and: "OHLCV service should be called for unique symbols only"
        1 * ohlcvService.updateOHLCVData("AAPL", null, null)
        1 * ohlcvService.updateOHLCVData("MSFT", null, null)

        and: "P&L should be recalculated for all positions"
        3 * databaseManager.updatePosition(_, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _)
    }

    def "should handle OHLCV update errors gracefully"() {
        given: "positions exist in database"
        def positions = [createSamplePosition(1L, "AAPL"), createSamplePosition(2L, "INVALID")]
        databaseManager.getPositions(null, null, null) >> positions.collect { pos ->
            [
                    id: pos.id,
                    symbol: pos.symbol,
                    position: pos.position,
                    side: pos.side.name(),
                    status: pos.status.name(),
                    trade_price: pos.tradePrice,
                    trade_value: pos.tradeValue,
                    init_portfolio_net_value: pos.initPortfolioNetValue,
                    last_price: pos.lastPrice,
                    last_value: pos.lastValue,
                    risk_unit: pos.riskUnit,
                    stop_percent: pos.stopPercent,
                    highest_after_trade: pos.highestAfterTrade,
                    stop_value_from_highest: pos.stopValueFromHighest,
                    last_bbmb: pos.lastBbmb,
                    bbmb_adj_percent: pos.bbmbAdjPercent,
                    stop_value_from_bbmb: pos.stopValueFromBbmb,
                    expand_or_contract: pos.expandOrContract?.name(),
                    effective_stop_value: pos.effectiveStopValue,
                    pnl_value: pos.pnlValue,
                    pnl_percent: pos.pnlPercent,
                    close_price: pos.closePrice,
                    created_date: Timestamp.valueOf(LocalDateTime.now()),
                    updated_date: Timestamp.valueOf(LocalDateTime.now())
            ]
        }

        and: "OHLCV service has mixed success/failure"
        ohlcvService.updateOHLCVData("AAPL", null, null) >> 5
        ohlcvService.updateOHLCVData("INVALID", null, null) >> { throw new RuntimeException("Invalid symbol") }

        and: "latest market price available for successful symbol"
        databaseManager.getLatestOHLCVData("AAPL", 1) >> [[close: 155.50d]]

        when: "updating OHLCV data for positions"
        def request = new PositionsUpdateRequest(false, true)
        def result = positionsService.updateOHLCVDataForPositions(request)

        then: "should handle partial failures"
        result.totalSymbols == 2
        result.ohlcvSuccessCount == 1
        result.ohlcvErrorCount == 1
        result.totalRecordsUpdated == 5
        result.pnlUpdatedCount == 1 // Only AAPL position updated
        result.ohlcvResults.size() == 2
        result.ohlcvResults.find { it.symbol == "AAPL" }.status == "success"
        result.ohlcvResults.find { it.symbol == "INVALID" }.status == "error"
    }

    def "should perform dry run without actual updates"() {
        given: "positions exist in database"
        def positions = [createSamplePosition(1L, "AAPL")]
        databaseManager.getPositions(null, null, null) >> positions.collect { pos ->
            [
                    id: pos.id,
                    symbol: pos.symbol,
                    position: pos.position,
                    side: pos.side.name(),
                    status: pos.status.name(),
                    trade_price: pos.tradePrice,
                    trade_value: pos.tradeValue,
                    init_portfolio_net_value: pos.initPortfolioNetValue,
                    last_price: pos.lastPrice,
                    last_value: pos.lastValue,
                    risk_unit: pos.riskUnit,
                    stop_percent: pos.stopPercent,
                    highest_after_trade: pos.highestAfterTrade,
                    stop_value_from_highest: pos.stopValueFromHighest,
                    last_bbmb: pos.lastBbmb,
                    bbmb_adj_percent: pos.bbmbAdjPercent,
                    stop_value_from_bbmb: pos.stopValueFromBbmb,
                    expand_or_contract: pos.expandOrContract?.name(),
                    effective_stop_value: pos.effectiveStopValue,
                    pnl_value: pos.pnlValue,
                    pnl_percent: pos.pnlPercent,
                    close_price: pos.closePrice,
                    created_date: Timestamp.valueOf(LocalDateTime.now()),
                    updated_date: Timestamp.valueOf(LocalDateTime.now())
            ]
        }

        when: "performing dry run"
        def request = new PositionsUpdateRequest(true, true)
        def result = positionsService.updateOHLCVDataForPositions(request)

        then: "should simulate updates without calling external services"
        result.totalSymbols == 1
        result.ohlcvSuccessCount == 1
        result.ohlcvErrorCount == 0
        result.pnlUpdatedCount == 1 // Simulated
        result.ohlcvResults.size() == 1
        result.ohlcvResults[0].message.contains("Dry run")

        and: "should not call OHLCV service or update database"
        0 * ohlcvService.updateOHLCVData(_, _, _)
        0 * databaseManager.updatePosition(_, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _)
    }

    def "should handle empty positions list"() {
        given: "no positions exist"
        databaseManager.getPositions(null, null, null) >> []

        when: "updating OHLCV data for positions"
        def request = new PositionsUpdateRequest(false, true)
        def result = positionsService.updateOHLCVDataForPositions(request)

        then: "should return appropriate response"
        result.totalSymbols == 0
        result.ohlcvSuccessCount == 0
        result.ohlcvErrorCount == 0
        result.totalRecordsUpdated == 0
        result.pnlUpdatedCount == 0
        result.totalPositions == 0
        result.summary == "No positions found to update"
    }

    def "should recalculate P&L for all positions"() {
        given: "positions exist in database"
        def positions = [
                createSamplePosition(1L, "AAPL"),
                createSamplePosition(2L, "MSFT")
        ]
        databaseManager.getPositions(null, null, null) >> positions.collect { pos ->
            [
                    id: pos.id,
                    symbol: pos.symbol,
                    position: pos.position,
                    side: pos.side.name(),
                    status: pos.status.name(),
                    trade_price: pos.tradePrice,
                    trade_value: pos.tradeValue,
                    init_portfolio_net_value: pos.initPortfolioNetValue,
                    last_price: pos.lastPrice,
                    last_value: pos.lastValue,
                    risk_unit: pos.riskUnit,
                    stop_percent: pos.stopPercent,
                    highest_after_trade: pos.highestAfterTrade,
                    stop_value_from_highest: pos.stopValueFromHighest,
                    last_bbmb: pos.lastBbmb,
                    bbmb_adj_percent: pos.bbmbAdjPercent,
                    stop_value_from_bbmb: pos.stopValueFromBbmb,
                    expand_or_contract: pos.expandOrContract?.name(),
                    effective_stop_value: pos.effectiveStopValue,
                    pnl_value: pos.pnlValue,
                    pnl_percent: pos.pnlPercent,
                    close_price: pos.closePrice,
                    created_date: Timestamp.valueOf(LocalDateTime.now()),
                    updated_date: Timestamp.valueOf(LocalDateTime.now())
            ]
        }

        and: "latest market prices are available"
        databaseManager.getLatestOHLCVData("AAPL", 1) >> [[close: 155.50d]]
        databaseManager.getLatestOHLCVData("MSFT", 1) >> [[close: 285.75d]]

        when: "recalculating P&L for all positions"
        def updatedCount = positionsService.recalculatePnLForAllPositions()

        then: "should update all positions with latest prices"
        updatedCount == 2

        and: "should call getPositionById for each position to check status"
        2 * databaseManager.getPositionById(_) >> { Long id ->
            def pos = positions.find { it.id == id }
            return [
                id: pos.id,
                symbol: pos.symbol,
                position: pos.position,
                side: pos.side.name(),
                status: pos.status.name(),
                trade_price: pos.tradePrice,
                trade_value: pos.tradeValue,
                init_portfolio_net_value: pos.initPortfolioNetValue,
                last_price: pos.lastPrice,
                last_value: pos.lastValue,
                risk_unit: pos.riskUnit,
                stop_percent: pos.stopPercent,
                highest_after_trade: pos.highestAfterTrade,
                stop_value_from_highest: pos.stopValueFromHighest,
                last_bbmb: pos.lastBbmb,
                bbmb_adj_percent: pos.bbmbAdjPercent,
                stop_value_from_bbmb: pos.stopValueFromBbmb,
                expand_or_contract: pos.expandOrContract?.name(),
                effective_stop_value: pos.effectiveStopValue,
                pnl_value: pos.pnlValue,
                pnl_percent: pos.pnlPercent,
                close_price: pos.closePrice,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
            ]
        }

        and: "should update positions with latest prices"
        2 * databaseManager.updatePosition(_, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _)
    }

    def "should handle status-aware P&L recalculation with mixed position types"() {
        given: "mixed OPEN and CLOSED positions exist in database"
        def openPosition = createSamplePosition(1L, "AAPL")
        openPosition.setStatus(Position.Status.OPEN)

        def closedPosition = createSamplePosition(2L, "MSFT")
        closedPosition.setStatus(Position.Status.CLOSED)
        closedPosition.setClosePrice(new BigDecimal("280.00"))

        def positions = [openPosition, closedPosition]

        databaseManager.getPositions(null, null, null) >> positions.collect { pos ->
            [
                    id: pos.id,
                    symbol: pos.symbol,
                    position: pos.position,
                    side: pos.side.name(),
                    status: pos.status.name(),
                    trade_price: pos.tradePrice,
                    trade_value: pos.tradeValue,
                    init_portfolio_net_value: pos.initPortfolioNetValue,
                    last_price: pos.lastPrice,
                    last_value: pos.lastValue,
                    risk_unit: pos.riskUnit,
                    stop_percent: pos.stopPercent,
                    highest_after_trade: pos.highestAfterTrade,
                    stop_value_from_highest: pos.stopValueFromHighest,
                    last_bbmb: pos.lastBbmb,
                    bbmb_adj_percent: pos.bbmbAdjPercent,
                    stop_value_from_bbmb: pos.stopValueFromBbmb,
                    expand_or_contract: pos.expandOrContract?.name(),
                    effective_stop_value: pos.effectiveStopValue,
                    pnl_value: pos.pnlValue,
                    pnl_percent: pos.pnlPercent,
                    close_price: pos.closePrice,
                    created_date: Timestamp.valueOf(LocalDateTime.now()),
                    updated_date: Timestamp.valueOf(LocalDateTime.now())
            ]
        }

        and: "latest market prices are available for OPEN positions"
        databaseManager.getLatestOHLCVData("AAPL", 1) >> [[close: 155.50d]]

        and: "mock getPositionById calls for status checking"
        databaseManager.getPositionById(1L) >> [
                id: openPosition.id,
                symbol: openPosition.symbol,
                position: openPosition.position,
                side: openPosition.side.name(),
                status: openPosition.status.name(),
                trade_price: openPosition.tradePrice,
                trade_value: openPosition.tradeValue,
                init_portfolio_net_value: openPosition.initPortfolioNetValue,
                last_price: openPosition.lastPrice,
                last_value: openPosition.lastValue,
                risk_unit: openPosition.riskUnit,
                stop_percent: openPosition.stopPercent,
                highest_after_trade: openPosition.highestAfterTrade,
                stop_value_from_highest: openPosition.stopValueFromHighest,
                last_bbmb: openPosition.lastBbmb,
                bbmb_adj_percent: openPosition.bbmbAdjPercent,
                stop_value_from_bbmb: openPosition.stopValueFromBbmb,
                expand_or_contract: openPosition.expandOrContract?.name(),
                effective_stop_value: openPosition.effectiveStopValue,
                pnl_value: openPosition.pnlValue,
                pnl_percent: openPosition.pnlPercent,
                close_price: openPosition.closePrice,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        when: "recalculating P&L for all positions with status-aware logic"
        def updatedCount = positionsService.recalculatePnLForAllPositions()

        then: "should update both positions (1 OPEN with market price, 1 CLOSED with close price)"
        updatedCount == 2

        and: "should update both positions in database"
        2 * databaseManager.updatePosition(_, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _)
    }

    def "should skip market price update for CLOSED position"() {
        given: "a CLOSED position exists"
        def closedPosition = createSamplePosition(1L, "AAPL")
        closedPosition.setStatus(Position.Status.CLOSED)
        closedPosition.setClosePrice(new BigDecimal("155.00"))

        databaseManager.getPositionById(1L) >> [
                id: closedPosition.id,
                symbol: closedPosition.symbol,
                position: closedPosition.position,
                side: closedPosition.side.name(),
                status: closedPosition.status.name(),
                trade_price: closedPosition.tradePrice,
                trade_value: closedPosition.tradeValue,
                init_portfolio_net_value: closedPosition.initPortfolioNetValue,
                last_price: closedPosition.lastPrice,
                last_value: closedPosition.lastValue,
                risk_unit: closedPosition.riskUnit,
                stop_percent: closedPosition.stopPercent,
                highest_after_trade: closedPosition.highestAfterTrade,
                stop_value_from_highest: closedPosition.stopValueFromHighest,
                last_bbmb: closedPosition.lastBbmb,
                bbmb_adj_percent: closedPosition.bbmbAdjPercent,
                stop_value_from_bbmb: closedPosition.stopValueFromBbmb,
                expand_or_contract: closedPosition.expandOrContract?.name(),
                effective_stop_value: closedPosition.effectiveStopValue,
                pnl_value: closedPosition.pnlValue,
                pnl_percent: closedPosition.pnlPercent,
                close_price: closedPosition.closePrice,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        when: "trying to update position price"
        def result = positionsService.updatePositionPrice(1L, new BigDecimal("170.00"))

        then: "should return position unchanged without database update"
        result.status == Position.Status.CLOSED
        result.closePrice == new BigDecimal("155.00")

        and: "should not update database"
        0 * databaseManager.updatePosition(_, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _)
    }

    def "should create position with open date"() {
        given: "a create position request with open date"
        def openDate = LocalDate.of(2024, 1, 15)
        def request = new CreatePositionRequest("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))
        request.setOpenDate(openDate)

        and: "symbol exists in instruments table"
        databaseManager.symbolExists("AAPL") >> true

        and: "database creates position successfully"
        databaseManager.createPosition(
                "AAPL",
                new BigDecimal("100"),
                "BUY",
                "OPEN",
                new BigDecimal("150.25"),
                new BigDecimal("15025.00"),
                null,
                null,
                null,
                null,
                java.sql.Date.valueOf(openDate)
        ) >> 1L

        when: "creating the position"
        def position = positionsService.createPosition(request)

        then: "position should be created with open date"
        position.id == 1L
        position.openDate == openDate
    }

    def "should create position with default open date when not provided"() {
        given: "a create position request without open date"
        def request = new CreatePositionRequest("AAPL", new BigDecimal("100"), Position.Side.BUY, new BigDecimal("150.25"))

        and: "symbol exists in instruments table"
        databaseManager.symbolExists("AAPL") >> true

        and: "database creates position successfully"
        databaseManager.createPosition(
                "AAPL",
                new BigDecimal("100"),
                "BUY",
                "OPEN",
                new BigDecimal("150.25"),
                new BigDecimal("15025.00"),
                null,
                null,
                null,
                null,
                _ as java.sql.Date // Should be today's date
        ) >> 1L

        when: "creating the position"
        def position = positionsService.createPosition(request)

        then: "position should be created with today's date as open date"
        position.id == 1L
        position.openDate == LocalDate.now()
    }

    def "should update position with open and close dates"() {
        given: "an existing position"
        def positionId = 1L
        def openDate = LocalDate.of(2024, 1, 15)
        def closeDate = LocalDate.of(2024, 2, 15)
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setOpenDate(openDate)
        updateRequest.setCloseDate(closeDate)

        and: "position exists in database"
        def positionData = [
                id: positionId,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.25"),
                trade_value: new BigDecimal("15025.00"),
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                close_price: null,
                open_date: null,
                close_date: null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "updating the position with dates"
        def updatedPosition = positionsService.updatePosition(positionId, updateRequest)

        then: "position should be updated with dates"
        updatedPosition.openDate == openDate
        updatedPosition.closeDate == closeDate

        and: "database should be updated with dates"
        1 * databaseManager.updatePosition(positionId, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _, _,
                                          java.sql.Date.valueOf(openDate), java.sql.Date.valueOf(closeDate))
    }

    def "should throw exception when close date is before open date"() {
        given: "an existing position with open date"
        def positionId = 1L
        def openDate = LocalDate.of(2024, 2, 15)
        def closeDate = LocalDate.of(2024, 1, 15) // Before open date
        def updateRequest = new UpdatePositionRequest()
        updateRequest.setCloseDate(closeDate)

        and: "position exists in database with open date"
        def positionData = [
                id: positionId,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "OPEN",
                trade_price: new BigDecimal("150.25"),
                trade_value: new BigDecimal("15025.00"),
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                close_price: null,
                open_date: java.sql.Date.valueOf(openDate),
                close_date: null,
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(positionId) >> positionData

        when: "updating position with close date before open date"
        positionsService.updatePosition(positionId, updateRequest)

        then: "should throw IllegalArgumentException"
        def exception = thrown(IllegalArgumentException)
        exception.message.contains("Close date cannot be before open date")
    }

    def "should handle position data mapping with date fields"() {
        given: "position data with date fields"
        def openDate = LocalDate.of(2024, 1, 15)
        def closeDate = LocalDate.of(2024, 2, 15)
        def positionData = [
                id: 1L,
                symbol: "AAPL",
                position: new BigDecimal("100"),
                side: "BUY",
                status: "CLOSED",
                trade_price: new BigDecimal("150.25"),
                trade_value: new BigDecimal("15025.00"),
                init_portfolio_net_value: null,
                last_price: null,
                last_value: null,
                risk_unit: null,
                stop_percent: null,
                highest_after_trade: null,
                stop_value_from_highest: null,
                last_bbmb: null,
                bbmb_adj_percent: null,
                stop_value_from_bbmb: null,
                expand_or_contract: null,
                effective_stop_value: null,
                pnl_value: null,
                pnl_percent: null,
                close_price: new BigDecimal("155.00"),
                open_date: java.sql.Date.valueOf(openDate),
                close_date: java.sql.Date.valueOf(closeDate),
                created_date: Timestamp.valueOf(LocalDateTime.now()),
                updated_date: Timestamp.valueOf(LocalDateTime.now())
        ]

        databaseManager.getPositionById(1L) >> positionData

        when: "getting position by ID"
        def optionalPosition = positionsService.getPositionById(1L)

        then: "should return position with correct date fields"
        optionalPosition.isPresent()
        def position = optionalPosition.get()
        position.openDate == openDate
        position.closeDate == closeDate
        position.status == Position.Status.CLOSED
    }


}
