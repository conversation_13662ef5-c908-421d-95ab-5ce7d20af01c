package com.investment.api.model;

import com.investment.model.Position;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Response model for position data.
 */
@Schema(description = "Portfolio position information")
public class PositionResponse {
    
    @Schema(description = "Unique position identifier", example = "1")
    private Long id;
    
    @Schema(description = "Financial instrument symbol", example = "AAPL")
    private String symbol;
    
    @Schema(description = "Quantity of shares/units held", example = "100.0")
    private BigDecimal position;
    
    @Schema(description = "Position direction")
    private Position.Side side;
    
    @Schema(description = "Position status")
    private Position.Status status;
    
    @Schema(description = "Price at which the position was opened", example = "150.25")
    private BigDecimal tradePrice;
    
    @Schema(description = "Total value when position was opened", example = "15025.00")
    private BigDecimal tradeValue;
    
    @Schema(description = "Portfolio net value when position was opened", example = "100000.00")
    private BigDecimal initPortfolioNetValue;
    
    @Schema(description = "Most recent market price", example = "155.75")
    private BigDecimal lastPrice;
    
    @Schema(description = "Current position value", example = "15575.00")
    private BigDecimal lastValue;
    
    @Schema(description = "Risk unit size", example = "1000.00")
    private BigDecimal riskUnit;
    
    @Schema(description = "Stop loss percentage", example = "0.02")
    private BigDecimal stopPercent;
    
    @Schema(description = "Highest price reached since position opened", example = "158.50")
    private BigDecimal highestAfterTrade;
    
    @Schema(description = "Stop loss price from highest", example = "155.33")
    private BigDecimal stopValueFromHighest;
    
    @Schema(description = "Last Bollinger Band Middle Band value", example = "152.30")
    private BigDecimal lastBbmb;
    
    @Schema(description = "BBMB adjustment percentage", example = "0.01")
    private BigDecimal bbmbAdjPercent;
    
    @Schema(description = "Stop loss price from BBMB", example = "150.78")
    private BigDecimal stopValueFromBbmb;
    
    @Schema(description = "BBMB expansion/contraction status")
    private Position.ExpandOrContract expandOrContract;
    
    @Schema(description = "Effective stop loss price", example = "155.33")
    private BigDecimal effectiveStopValue;
    
    @Schema(description = "Profit/Loss in currency value", example = "550.00")
    private BigDecimal pnlValue;
    
    @Schema(description = "Profit/Loss percentage", example = "0.0366")
    private BigDecimal pnlPercent;

    @Schema(description = "Price at which the position was closed", example = "155.75")
    private BigDecimal closePrice;

    @Schema(description = "Date when the position was opened", example = "2024-01-15")
    private LocalDate openDate;

    @Schema(description = "Date when the position was closed", example = "2024-02-15")
    private LocalDate closeDate;

    @Schema(description = "When the position was created")
    private LocalDateTime createdDate;

    @Schema(description = "When the position was last updated")
    private LocalDateTime updatedDate;
    
    // Default constructor
    public PositionResponse() {}
    
    // Constructor from Position entity
    public PositionResponse(Position position) {
        this.id = position.getId();
        this.symbol = position.getSymbol();
        this.position = position.getPosition();
        this.side = position.getSide();
        this.status = position.getStatus();
        this.tradePrice = position.getTradePrice();
        this.tradeValue = position.getTradeValue();
        this.initPortfolioNetValue = position.getInitPortfolioNetValue();
        this.lastPrice = position.getLastPrice();
        this.lastValue = position.getLastValue();
        this.riskUnit = position.getRiskUnit();
        this.stopPercent = position.getStopPercent();
        this.highestAfterTrade = position.getHighestAfterTrade();
        this.stopValueFromHighest = position.getStopValueFromHighest();
        this.lastBbmb = position.getLastBbmb();
        this.bbmbAdjPercent = position.getBbmbAdjPercent();
        this.stopValueFromBbmb = position.getStopValueFromBbmb();
        this.expandOrContract = position.getExpandOrContract();
        this.effectiveStopValue = position.getEffectiveStopValue();
        this.pnlValue = position.getPnlValue();
        this.pnlPercent = position.getPnlPercent();
        this.closePrice = position.getClosePrice();
        this.createdDate = position.getCreatedDate();
        this.updatedDate = position.getUpdatedDate();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    
    public BigDecimal getPosition() { return position; }
    public void setPosition(BigDecimal position) { this.position = position; }
    
    public Position.Side getSide() { return side; }
    public void setSide(Position.Side side) { this.side = side; }
    
    public Position.Status getStatus() { return status; }
    public void setStatus(Position.Status status) { this.status = status; }
    
    public BigDecimal getTradePrice() { return tradePrice; }
    public void setTradePrice(BigDecimal tradePrice) { this.tradePrice = tradePrice; }
    
    public BigDecimal getTradeValue() { return tradeValue; }
    public void setTradeValue(BigDecimal tradeValue) { this.tradeValue = tradeValue; }
    
    public BigDecimal getInitPortfolioNetValue() { return initPortfolioNetValue; }
    public void setInitPortfolioNetValue(BigDecimal initPortfolioNetValue) { this.initPortfolioNetValue = initPortfolioNetValue; }
    
    public BigDecimal getLastPrice() { return lastPrice; }
    public void setLastPrice(BigDecimal lastPrice) { this.lastPrice = lastPrice; }
    
    public BigDecimal getLastValue() { return lastValue; }
    public void setLastValue(BigDecimal lastValue) { this.lastValue = lastValue; }
    
    public BigDecimal getRiskUnit() { return riskUnit; }
    public void setRiskUnit(BigDecimal riskUnit) { this.riskUnit = riskUnit; }
    
    public BigDecimal getStopPercent() { return stopPercent; }
    public void setStopPercent(BigDecimal stopPercent) { this.stopPercent = stopPercent; }
    
    public BigDecimal getHighestAfterTrade() { return highestAfterTrade; }
    public void setHighestAfterTrade(BigDecimal highestAfterTrade) { this.highestAfterTrade = highestAfterTrade; }
    
    public BigDecimal getStopValueFromHighest() { return stopValueFromHighest; }
    public void setStopValueFromHighest(BigDecimal stopValueFromHighest) { this.stopValueFromHighest = stopValueFromHighest; }
    
    public BigDecimal getLastBbmb() { return lastBbmb; }
    public void setLastBbmb(BigDecimal lastBbmb) { this.lastBbmb = lastBbmb; }
    
    public BigDecimal getBbmbAdjPercent() { return bbmbAdjPercent; }
    public void setBbmbAdjPercent(BigDecimal bbmbAdjPercent) { this.bbmbAdjPercent = bbmbAdjPercent; }
    
    public BigDecimal getStopValueFromBbmb() { return stopValueFromBbmb; }
    public void setStopValueFromBbmb(BigDecimal stopValueFromBbmb) { this.stopValueFromBbmb = stopValueFromBbmb; }
    
    public Position.ExpandOrContract getExpandOrContract() { return expandOrContract; }
    public void setExpandOrContract(Position.ExpandOrContract expandOrContract) { this.expandOrContract = expandOrContract; }
    
    public BigDecimal getEffectiveStopValue() { return effectiveStopValue; }
    public void setEffectiveStopValue(BigDecimal effectiveStopValue) { this.effectiveStopValue = effectiveStopValue; }
    
    public BigDecimal getPnlValue() { return pnlValue; }
    public void setPnlValue(BigDecimal pnlValue) { this.pnlValue = pnlValue; }
    
    public BigDecimal getPnlPercent() { return pnlPercent; }
    public void setPnlPercent(BigDecimal pnlPercent) { this.pnlPercent = pnlPercent; }

    public BigDecimal getClosePrice() { return closePrice; }
    public void setClosePrice(BigDecimal closePrice) { this.closePrice = closePrice; }

    public LocalDateTime getCreatedDate() { return createdDate; }
    public void setCreatedDate(LocalDateTime createdDate) { this.createdDate = createdDate; }
    
    public LocalDateTime getUpdatedDate() { return updatedDate; }
    public void setUpdatedDate(LocalDateTime updatedDate) { this.updatedDate = updatedDate; }
    
    /**
     * Get a summary string for the position.
     */
    public String getSummary() {
        return String.format("%s %s %s @ %s (P&L: %s / %.2f%%)",
                side, position, symbol, tradePrice, 
                pnlValue != null ? pnlValue : "N/A",
                pnlPercent != null ? pnlPercent.multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO);
    }
}
